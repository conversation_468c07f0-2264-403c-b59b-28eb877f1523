//
//  PromotionCenterMainViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/3/26.
//

import UIKit
import JXSegmentedView

class PromotionCenterMainViewController: BaseViewController {
    
    // MARK: - Properties
    
    // 页面标题数组
    private let titles = ["带货中心", "销售中心", "我的带货"]
    
    // 子页面控制器数组
    private var subViewControllers: [UIViewController] = []
    
    // JX分段控制器
    private var segmentedView: JXSegmentedView!
    private var segmentedDataSource: JXSegmentedTitleDataSource!
    private var listContainerView: JXSegmentedListContainerView!

    // 自定义底部TabBar
    private var customBottomTabBar: PromotionCenterTabBar!

    // 状态栏背景视图
    private var statusBarBackgroundView: UIView!

    // 当前选中的索引
    private var currentIndex: Int = 0
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()

        // 设置标题
        navTitle = "带货中心"

        // 设置视图背景色
        view.backgroundColor = .white
        contentView.backgroundColor = .white

        // 设置状态栏背景视图
        setupStatusBarBackground()

        // 初始化子控制器
        setupSubViewControllers()

        // 设置UI
        setupUI()

        // 设置底部TabBar
        setupBottomTabBar()

        // 设置初始导航栏样式
        updateNavigationBarStyle(for: 0)

        // 监听添加右侧按钮的通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAddRightButtonNotification(_:)),
            name: NSNotification.Name("AddRightButtonToNavBar"),
            object: nil
        )
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        // 只有在不是完全自定义布局的页面时才操作导航栏
        if currentIndex != 2 {
            // 确保导航栏在最上层
            bringNavBarToFront()

            // 确保右侧按钮也在最上层（如果存在）
            bringRightButtonToFront()
        }
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        // 更新底部TabBar的高度以适应安全区域
        updateBottomTabBarHeight()

        // 只有在不是完全自定义布局的页面时才操作右侧按钮
        if currentIndex != 2 {
            // 确保右侧按钮在最上层
            bringRightButtonToFront()
        }
    }
    
    // MARK: - Setup
    private func setupSubViewControllers() {
        // 创建三个子页面控制器
        let promotionCenterVC = PromotionCenterViewController()
        let salesCenterVC = SalesCenterViewController()
        let myPromotionVC = MyPromotionViewController()

        subViewControllers = [promotionCenterVC, salesCenterVC, myPromotionVC]

        // 建立父子关系
        for childVC in subViewControllers {
            addChild(childVC)
            childVC.didMove(toParent: self)
        }
    }
    
    private func setupUI() {
        // 设置JX分段控制器（隐藏，但需要用于页面管理）
        setupSegmentedView()

        // 设置列表容器
        setupListContainer()
    }
    
    private func setupSegmentedView() {
        // 创建数据源
        segmentedDataSource = JXSegmentedTitleDataSource()
        segmentedDataSource.titles = titles
        segmentedDataSource.titleNormalColor = UIColor(hex: "#666666")
        segmentedDataSource.titleSelectedColor = UIColor(hex: "#FB6C04")
        segmentedDataSource.titleNormalFont = UIFont.systemFont(ofSize: 16)
        segmentedDataSource.titleSelectedFont = UIFont.boldSystemFont(ofSize: 16)
        segmentedDataSource.itemSpacing = 40
        segmentedDataSource.isItemSpacingAverageEnabled = false

        // 创建分段控制器
        segmentedView = JXSegmentedView()
        segmentedView.backgroundColor = .white
        segmentedView.delegate = self
        segmentedView.dataSource = segmentedDataSource

        // 创建指示器
        let indicator = JXSegmentedIndicatorLineView()
        indicator.indicatorColor = UIColor(hex: "#FB6C04")
        indicator.indicatorWidth = 20
        indicator.indicatorHeight = 3
        indicator.indicatorCornerRadius = 1.5
        segmentedView.indicators = [indicator]

        // 隐藏分段控制器，因为我们使用底部TabBar
        segmentedView.isHidden = true

        // 添加到视图（虽然隐藏，但需要用于页面管理）
        contentView.addSubview(segmentedView)
        segmentedView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(0) // 设置高度为0，因为已隐藏
        }
    }
    
    private func setupListContainer() {
        // 创建列表容器
        listContainerView = JXSegmentedListContainerView(dataSource: self)
        listContainerView.backgroundColor = .white

        // 关联分段控制器和列表容器
        segmentedView.listContainer = listContainerView

        // 添加到视图
        contentView.addSubview(listContainerView)
        listContainerView.snp.makeConstraints { make in
            make.top.equalTo(segmentedView.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-93) // 为底部TabBar留出空间 (59 + 34预估安全区域)
        }
    }

    private func setupBottomTabBar() {
        // 创建自定义底部TabBar
        customBottomTabBar = PromotionCenterTabBar()
        customBottomTabBar.delegate = self

        // 添加到主视图（不是contentView）
        view.addSubview(customBottomTabBar)
        customBottomTabBar.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(59 + 34) // 59是TabBar高度，34是预估的安全区域高度，会在viewDidLayoutSubviews中更新
        }

        // 设置默认选中项
        customBottomTabBar.setSelectedIndex(0)
    }
    
    // MARK: - Override BaseViewController Methods
    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .lightContent // 白色状态栏内容，适配深色背景
    }

    override func backButtonTapped() {
        // 所有子页面的返回按钮都返回到同一个上一页
        super.backButtonTapped()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - Notification Handlers
    @objc private func handleAddRightButtonNotification(_ notification: Notification) {
        if let button = notification.object as? UIButton {
            print("🔧 通过通知接收到按钮，添加到导航栏")

            // 移除按钮（如果已存在）
            button.removeFromSuperview()

            // 添加到导航栏
            navBar.addSubview(button)

            // 设置约束
            button.snp.remakeConstraints { make in
                make.right.equalToSuperview().offset(-16)
                make.centerY.equalToSuperview()
                make.width.equalTo(104)
                make.height.equalTo(27)
            }

            // 确保按钮在最上层
            navBar.bringSubviewToFront(button)

            // 设置按钮可见性
            button.isHidden = false
            button.alpha = 1.0

            print("🔧 通过通知添加按钮完成")
        }
    }

    // MARK: - Private Methods
    private func setupStatusBarBackground() {
        // 创建状态栏背景视图
        statusBarBackgroundView = UIView()
        statusBarBackgroundView.backgroundColor = UIColor(hex: "#FF8F1F")

        // 添加到主视图的最底层
        view.insertSubview(statusBarBackgroundView, at: 0)
        statusBarBackgroundView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.top)
        }
    }

    private func updateStatusBarBackground(color: UIColor) {
        statusBarBackgroundView.backgroundColor = color
    }

    private func bringRightButtonToFront() {
        // 遍历导航栏的所有子视图，找到右侧按钮并置于最前
        navBar.subviews.forEach { subview in
            if subview is UIButton && subview.tag != 0 { // 排除返回按钮
                navBar.bringSubviewToFront(subview)
            }
        }
    }

    private func updateBottomTabBarHeight() {
        let safeAreaBottom = view.safeAreaInsets.bottom
        let tabBarHeight: CGFloat = 59 + safeAreaBottom

        customBottomTabBar.snp.updateConstraints { make in
            make.height.equalTo(tabBarHeight)
        }

        // 同时更新listContainerView的底部约束
        listContainerView.snp.updateConstraints { make in
            make.bottom.equalToSuperview().offset(-tabBarHeight)
        }
    }

    private func updateNavigationBarStyle(for index: Int) {
        switch index {
        case 0:
            // 带货中心 - 橙色导航栏，包含状态栏
            navBar.isHidden = false
            navBar.backgroundColor = UIColor(hex: "#FF8F1F")
            titleLabel.textColor = .white
            // 使用白色返回按钮，如果没有则使用默认的
            if let whiteBackImage = UIImage(named: "nav_back_white") {
                backButton.setImage(whiteBackImage, for: .normal)
            } else {
                backButton.setImage(UIImage(named: "nav_back"), for: .normal)
            }
            navTitle = "带货中心"

            // 设置状态栏区域背景色
            updateStatusBarBackground(color: UIColor(hex: "#FF8F1F"))

        case 1:
            // 销售中心 - 蓝色导航栏
            navBar.isHidden = false
            navBar.backgroundColor = UIColor(hex: "#007AFF")
            titleLabel.textColor = .white
            if let whiteBackImage = UIImage(named: "nav_back_white") {
                backButton.setImage(whiteBackImage, for: .normal)
            } else {
                backButton.setImage(UIImage(named: "nav_back"), for: .normal)
            }
            navTitle = "销售中心"

            // 设置状态栏区域背景色
            updateStatusBarBackground(color: UIColor(hex: "#007AFF"))

        case 2:
            // 我的带货 - 使用完全自定义布局，隐藏主控制器的导航栏
            // MyPromotionViewController 使用 useFullCustomLayout = true，完全控制自己的布局
            navBar.isHidden = true

            // 设置状态栏区域背景色为渐变色，与子控制器保持一致
            updateStatusBarBackground(color: UIColor(hex: "#FF8D36"))

        default:
            // 默认样式
            navBar.isHidden = false
            navBar.backgroundColor = .white
            titleLabel.textColor = UIColor(hex: "#333333")
            backButton.setImage(UIImage(named: "nav_back"), for: .normal)

            // 设置状态栏区域背景色
            updateStatusBarBackground(color: .white)
        }
    }
}

// MARK: - PromotionCenterTabBarDelegate
extension PromotionCenterMainViewController: PromotionCenterTabBarDelegate {
    func tabBar(_ tabBar: PromotionCenterTabBar, didSelectItemAt index: Int) {
        currentIndex = index

        // 通过JX分段控制器切换页面
        segmentedView.selectItemAt(index: index)

        // 根据选中的页面更新导航栏样式
        updateNavigationBarStyle(for: index)
    }
}

// MARK: - JXSegmentedViewDelegate
extension PromotionCenterMainViewController: JXSegmentedViewDelegate {
    func segmentedView(_ segmentedView: JXSegmentedView, didSelectedItemAt index: Int) {
        currentIndex = index

        // 同步更新底部TabBar的选中状态
        customBottomTabBar.setSelectedIndex(index)

        // 根据选中的页面更新导航栏样式
        updateNavigationBarStyle(for: index)
    }
}

// MARK: - JXSegmentedListContainerViewDataSource
extension PromotionCenterMainViewController: JXSegmentedListContainerViewDataSource {
    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        return subViewControllers.count
    }

    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        return subViewControllers[index] as! JXSegmentedListContainerViewListDelegate
    }
}

// MARK: - PromotionCenterTabBar
protocol PromotionCenterTabBarDelegate: AnyObject {
    func tabBar(_ tabBar: PromotionCenterTabBar, didSelectItemAt index: Int)
}

class PromotionCenterTabBar: UIView {

    weak var delegate: PromotionCenterTabBarDelegate?

    private var buttons: [PromotionCenterTabBarButton] = []
    private var selectedIndex: Int = 0

    // TabBar项配置
    private let tabItems = [
        PromotionCenterTabBarItem(
            title: "带货中心",
            normalIcon: "promotion_center_tab_normal",
            selectedIcon: "promotion_center_tab_selected"
        ),
        PromotionCenterTabBarItem(
            title: "销售中心",
            normalIcon: "sales_center_tab_normal",
            selectedIcon: "sales_center_tab_selected"
        ),
        PromotionCenterTabBarItem(
            title: "我的带货",
            normalIcon: "my_promotion_tab_normal",
            selectedIcon: "my_promotion_tab_selected"
        )
    ]

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = .white

        // 添加顶部分隔线
        let separatorLine = UIView()
        separatorLine.backgroundColor = UIColor(hex: "#EEEEEE")
        addSubview(separatorLine)
        separatorLine.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(0.5)
        }

        // 创建按钮
        setupButtons()
    }

    private func setupButtons() {
        // 清除现有按钮
        buttons.forEach { $0.removeFromSuperview() }
        buttons.removeAll()

        // 计算按钮宽度
        let buttonWidth = UIScreen.main.bounds.width / CGFloat(tabItems.count)

        for (index, item) in tabItems.enumerated() {
            let button = PromotionCenterTabBarButton(item: item)
            button.tag = index
            button.addTarget(self, action: #selector(buttonTapped(_:)), for: .touchUpInside)

            addSubview(button)
            buttons.append(button)

            button.snp.makeConstraints { make in
                make.top.equalToSuperview().offset(8)
                make.bottom.equalTo(safeAreaLayoutGuide.snp.bottom).offset(-8)
                make.width.equalTo(buttonWidth)
                make.left.equalToSuperview().offset(CGFloat(index) * buttonWidth)
            }
        }

        // 设置默认选中状态
        updateSelectedState()
    }

    @objc private func buttonTapped(_ sender: PromotionCenterTabBarButton) {
        let index = sender.tag
        setSelectedIndex(index)
        delegate?.tabBar(self, didSelectItemAt: index)
    }

    func setSelectedIndex(_ index: Int) {
        guard index >= 0 && index < buttons.count else { return }
        selectedIndex = index
        updateSelectedState()
    }

    private func updateSelectedState() {
        for (index, button) in buttons.enumerated() {
            button.setSelected(index == selectedIndex)
        }
    }
}

// MARK: - PromotionCenterTabBarItem
struct PromotionCenterTabBarItem {
    let title: String
    let normalIcon: String
    let selectedIcon: String
}

// MARK: - PromotionCenterTabBarButton
class PromotionCenterTabBarButton: UIButton {

    private let item: PromotionCenterTabBarItem
    private let iconImageView: UIImageView
    private let customTitleLabel: UILabel

    init(item: PromotionCenterTabBarItem) {
        self.item = item
        self.iconImageView = UIImageView()
        self.customTitleLabel = UILabel()

        super.init(frame: .zero)

        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        // 配置图标
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.image = UIImage(named: item.normalIcon)
        addSubview(iconImageView)

        // 配置标题
        customTitleLabel.text = item.title
        customTitleLabel.font = UIFont.systemFont(ofSize: 10)
        customTitleLabel.textColor = UIColor(hex: "#999999")
        customTitleLabel.textAlignment = .center
        addSubview(customTitleLabel)

        // 设置约束
        iconImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(4)
            make.width.height.equalTo(28)
        }

        customTitleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(iconImageView.snp.bottom).offset(2)
            make.left.right.equalToSuperview().inset(4)
        }
    }

    func setSelected(_ selected: Bool) {
        if selected {
            iconImageView.image = UIImage(named: item.selectedIcon)
            customTitleLabel.textColor = UIColor(hex: "#000000").withAlphaComponent(0.85)
        } else {
            iconImageView.image = UIImage(named: item.normalIcon)
            customTitleLabel.textColor = UIColor(hex: "#999999")
        }
    }
}

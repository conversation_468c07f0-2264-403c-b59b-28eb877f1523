//
//  SimpleMyPromotionViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by AI Assistant on 2025/3/26.
//

import UIKit
import JXSegmentedView

class SimpleMyPromotionViewController: UIViewController {
    
    // MARK: - UI Components
    
    // 渐变背景视图
    private lazy var backgroundGradientView: UIView = {
        let view = UIView()
        // 设置橙色渐变背景
        view.applyGradient(
            colors: [
                UIColor(hex: "#FF8D36"), // 橙色
                UIColor(hex: "#FF6B9D")  // 粉色
            ],
            direction: .vertical
        )
        return view
    }()
    
    // 自定义导航栏
    private lazy var customNavigationBar: UIView = {
        let view = UIView()
        view.backgroundColor = .clear // 透明背景，让渐变透过
        
        // 返回按钮
        let backButton = UIButton(type: .custom)
        backButton.setImage(UIImage(systemName: "chevron.left"), for: .normal)
        backButton.tintColor = .white
        backButton.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        view.addSubview(backButton)
        
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "个人中心"
        titleLabel.textColor = .white
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textAlignment = .center
        view.addSubview(titleLabel)
        
        // 编辑按钮
        let editButton = UIButton(type: .custom)
        editButton.setTitle("编辑", for: .normal)
        editButton.setTitleColor(.white, for: .normal)
        editButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        editButton.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        editButton.layer.cornerRadius = 15
        editButton.layer.borderWidth = 1
        editButton.layer.borderColor = UIColor.white.cgColor
        view.addSubview(editButton)
        
        // 设置约束
        backButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(44)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        editButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(60)
            make.height.equalTo(30)
        }
        
        return view
    }()
    
    // 内容视图
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        
        // 添加一个占位标签
        let placeholderLabel = UILabel()
        placeholderLabel.text = "我的带货页面"
        placeholderLabel.textColor = UIColor(hex: "#333333")
        placeholderLabel.font = UIFont.systemFont(ofSize: 16)
        placeholderLabel.textAlignment = .center
        view.addSubview(placeholderLabel)
        
        placeholderLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        return view
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置视图背景色
        view.backgroundColor = .white
        
        // 设置UI
        setupUI()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 更新渐变层frame
        backgroundGradientView.updateGradientFrame()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        // 添加渐变背景 - 从屏幕最顶部开始，包括状态栏
        view.addSubview(backgroundGradientView)
        backgroundGradientView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(200) // 设置一个合适的高度
        }
        
        // 添加自定义导航栏 - 在安全区域内，覆盖在渐变背景上
        view.addSubview(customNavigationBar)
        customNavigationBar.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        
        // 添加内容视图 - 在渐变背景下方
        view.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.top.equalTo(backgroundGradientView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    // MARK: - Actions
    
    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate

extension SimpleMyPromotionViewController: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
    
    func listDidAppear() {
        print("我的带货页面显示")
    }
    
    func listDidDisappear() {
        print("我的带货页面隐藏")
    }
}

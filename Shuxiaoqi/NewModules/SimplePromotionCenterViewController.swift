//
//  SimplePromotionCenterViewController.swift
//  Shuxiaoqi
//
//  Created by AI Assistant on 2025/3/26.
//

import UIKit
import JXSegmentedView

class SimplePromotionCenterViewController: UIViewController {
    
    // MARK: - UI Components
    
    // 自定义导航栏
    private lazy var customNavigationBar: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#FF8F1F")
        
        // 返回按钮
        let backButton = UIButton(type: .custom)
        backButton.setImage(UIImage(systemName: "chevron.left"), for: .normal)
        backButton.tintColor = .white
        backButton.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        view.addSubview(backButton)
        
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "带货中心"
        titleLabel.textColor = .white
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textAlignment = .center
        view.addSubview(titleLabel)
        
        // 右侧按钮
        let rightButton = UIButton(type: .custom)
        rightButton.setTitle("已带货商品", for: .normal)
        rightButton.setTitleColor(.white, for: .normal)
        rightButton.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        rightButton.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        rightButton.layer.cornerRadius = 13.5
        rightButton.layer.borderWidth = 1
        rightButton.layer.borderColor = UIColor.white.cgColor
        view.addSubview(rightButton)
        
        // 设置约束
        backButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(44)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        rightButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(104)
            make.height.equalTo(27)
        }
        
        return view
    }()
    
    // 内容视图
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        
        // 添加一个占位标签
        let placeholderLabel = UILabel()
        placeholderLabel.text = "带货中心页面"
        placeholderLabel.textColor = UIColor(hex: "#333333")
        placeholderLabel.font = UIFont.systemFont(ofSize: 16)
        placeholderLabel.textAlignment = .center
        view.addSubview(placeholderLabel)
        
        placeholderLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        return view
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置视图背景色
        view.backgroundColor = .white
        
        // 设置UI
        setupUI()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        // 添加自定义导航栏
        view.addSubview(customNavigationBar)
        customNavigationBar.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        
        // 添加内容视图
        view.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.top.equalTo(customNavigationBar.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    // MARK: - Actions
    
    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate

extension SimplePromotionCenterViewController: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
    
    func listDidAppear() {
        print("带货中心页面显示")
    }
    
    func listDidDisappear() {
        print("带货中心页面隐藏")
    }
}

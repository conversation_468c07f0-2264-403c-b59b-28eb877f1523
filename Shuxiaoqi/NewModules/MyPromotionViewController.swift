//
//  MyPromotionViewController.swift
//  <PERSON><PERSON><PERSON>qi
//
//  Created by yongsheng ye on 2025/3/26.
//

import UIKit
import JXSegmentedView
import Kingfisher

class MyPromotionViewController: BaseViewController {

    // MARK: - Properties

    // 背景渐变视图
    private lazy var backgroundGradientView: UIView = {
        let view = UIView()
        // 设置橙色渐变背景 (从设计图看是橙色到粉色的渐变)
        view.applyGradient(
            colors: [
                UIColor(hex: "#FF8D36"), // 橙色
                UIColor(hex: "#FF6B9D")  // 粉色
            ],
            direction: .vertical
        )
        return view
    }()

    // 滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        scrollView.contentInsetAdjustmentBehavior = .never
        return scrollView
    }()

    // 内容容器视图
    private lazy var scrollContentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    // 用户信息区域
    private lazy var userInfoView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear

        // 头像
        view.addSubview(avatarImageView)
        // 用户名
        view.addSubview(nameLabel)
        // 等级标签容器
        view.addSubview(levelTagsContainer)
        // 粉丝数和作品数
        view.addSubview(fansLabel)
        view.addSubview(worksLabel)
        // 位置图标
        view.addSubview(locationButton)

        return view
    }()

    // 头像
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "default_avatar")
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 30
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor.white.withAlphaComponent(0.3)
        imageView.layer.borderWidth = 2
        imageView.layer.borderColor = UIColor.white.cgColor
        return imageView
    }()

    // 用户名
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.text = "陈佳佳"
        label.textColor = .white
        label.font = UIFont.boldSystemFont(ofSize: 18)
        return label
    }()

    // 等级标签容器
    private lazy var levelTagsContainer: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 8
        stackView.alignment = .center

        // 创建等级标签
        let levelTag1 = createLevelTag(text: "优质带货人")
        let levelTag2 = createLevelTag(text: "带货达人")

        stackView.addArrangedSubview(levelTag1)
        stackView.addArrangedSubview(levelTag2)

        return stackView
    }()

    // 粉丝数标签
    private lazy var fansLabel: UILabel = {
        let label = UILabel()
        label.text = "粉丝 1280"
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 14)
        return label
    }()

    // 作品数标签
    private lazy var worksLabel: UILabel = {
        let label = UILabel()
        label.text = "获赞 158.9w"
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 14)
        return label
    }()

    // 位置按钮
    private lazy var locationButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(systemName: "location"), for: .normal)
        button.tintColor = .white
        return button
    }()

    // 自定义导航栏
    private lazy var customNavigationBar: UIView = {
        let view = UIView()
        view.backgroundColor = .clear

        // 返回按钮
        let backButton = UIButton(type: .custom)
        backButton.setImage(UIImage(systemName: "chevron.left"), for: .normal)
        backButton.tintColor = .white
        backButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        backButton.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        view.addSubview(backButton)

        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "个人中心"
        titleLabel.textColor = .white
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textAlignment = .center
        view.addSubview(titleLabel)

        // 编辑按钮
        let editButton = UIButton(type: .custom)
        editButton.setTitle("编辑", for: .normal)
        editButton.setTitleColor(.white, for: .normal)
        editButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        editButton.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        editButton.layer.cornerRadius = 15
        editButton.layer.borderWidth = 1
        editButton.layer.borderColor = UIColor.white.cgColor
        editButton.addTarget(self, action: #selector(editButtonTapped), for: .touchUpInside)
        view.addSubview(editButton)

        // 设置约束
        backButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(44)
        }

        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        editButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(60)
            make.height.equalTo(30)
        }

        return view
    }()

    // 数据统计卡片
    private lazy var statsCardView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 16 // 设置16pt圆角
        view.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowOpacity = 1
        view.layer.shadowRadius = 8

        // 创建统计项
        let stats = [
            ("总销售额", "¥892,651"),
            ("本月销售", "¥56,892"),
            ("待结算", "¥12,368")
        ]

        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .center
        view.addSubview(stackView)

        for (index, stat) in stats.enumerated() {
            let containerView = UIView()

            let titleLabel = UILabel()
            titleLabel.text = stat.0
            titleLabel.textColor = UIColor(hex: "#999999")
            titleLabel.font = UIFont.systemFont(ofSize: 12)
            titleLabel.textAlignment = .center
            containerView.addSubview(titleLabel)

            let valueLabel = UILabel()
            valueLabel.text = stat.1
            valueLabel.textColor = UIColor(hex: "#333333")
            valueLabel.font = UIFont.boldSystemFont(ofSize: 18)
            valueLabel.textAlignment = .center
            containerView.addSubview(valueLabel)

            // 设置约束
            valueLabel.snp.makeConstraints { make in
                make.top.equalToSuperview().offset(16)
                make.centerX.equalToSuperview()
            }

            titleLabel.snp.makeConstraints { make in
                make.top.equalTo(valueLabel.snp.bottom).offset(4)
                make.centerX.equalToSuperview()
                make.bottom.equalToSuperview().offset(-16)
            }

            stackView.addArrangedSubview(containerView)

            // 添加分隔线（除了最后一个）
            if index < stats.count - 1 {
                let separatorLine = UIView()
                separatorLine.backgroundColor = UIColor(hex: "#EEEEEE")
                containerView.addSubview(separatorLine)
                separatorLine.snp.makeConstraints { make in
                    make.right.equalToSuperview()
                    make.centerY.equalToSuperview()
                    make.width.equalTo(0.5)
                    make.height.equalTo(40)
                }
            }
        }

        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        return view
    }()

    // 功能按钮区域
    private lazy var functionsView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 16 // 设置16pt圆角
        view.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowOpacity = 1
        view.layer.shadowRadius = 8

        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .center
        view.addSubview(stackView)

        // 创建功能按钮
        let functions = [
            ("商品管理", "bag"),
            ("提现中心", "creditcard"),
            ("客服中心", "headphones")
        ]

        for function in functions {
            let button = createFunctionButton(title: function.0, iconName: function.1)
            stackView.addArrangedSubview(button)
        }

        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }

        return view
    }()

    // 推荐商品区域
    private lazy var recommendedProductsView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F8F9FA")
        view.layer.cornerRadius = 16 // 设置16pt圆角
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner] // 只设置顶部圆角

        // 标题区域
        let headerView = UIView()
        view.addSubview(headerView)

        let titleLabel = UILabel()
        titleLabel.text = "推荐商品"
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.font = UIFont.boldSystemFont(ofSize: 16)
        headerView.addSubview(titleLabel)

        let moreButton = UIButton(type: .custom)
        moreButton.setTitle("查看全部 >", for: .normal)
        moreButton.setTitleColor(UIColor(hex: "#999999"), for: .normal)
        moreButton.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        headerView.addSubview(moreButton)

        // 商品集合视图
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 12
        layout.sectionInset = UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16)

        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(RecommendedProductCell.self, forCellWithReuseIdentifier: "RecommendedProductCell")
        collectionView.isScrollEnabled = false
        view.addSubview(collectionView)
        self.productsCollectionView = collectionView

        // 设置约束
        headerView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(50)
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }

        moreButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }

        collectionView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }

        return view
    }()

    // 商品集合视图
    private var productsCollectionView: UICollectionView!



    // 示例商品数据
    private let recommendedProducts = [
        ["title": "2025春季新款连衣裙", "subtitle": "原创女风收腰显瘦", "price": "¥499", "originalPrice": "¥699", "image": "product1"],
        ["title": "法国进口高端护肤精华生活补水保湿提亮", "subtitle": "", "price": "¥688", "originalPrice": "¥899", "image": "product2"],
        ["title": "2025新款法国进口香水", "subtitle": "持久留香", "price": "¥299", "originalPrice": "¥399", "image": "product3"],
        ["title": "2025新款秋冬真皮女士手提包", "subtitle": "", "price": "¥688", "originalPrice": "¥899", "image": "product4"]
    ]

    // 右侧按钮
    private lazy var rightButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("编辑", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        button.layer.cornerRadius = 15
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.white.cgColor
        button.addTarget(self, action: #selector(rightButtonTapped), for: .touchUpInside)
        return button
    }()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()

        // 使用完全自定义布局，隐藏默认导航栏
        useFullCustomLayout = true

        // 设置UI
        setupUI()

        // 加载用户数据
        loadUserData()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        // 更新渐变层frame
        backgroundGradientView.updateGradientFrame()
    }



    // MARK: - Setup
    private func setupUI() {
        // 设置渐变背景 - 从屏幕最顶部开始，包括状态栏
        view.addSubview(backgroundGradientView)
        backgroundGradientView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(239)
        }

        // 添加自定义导航栏 - 在安全区域内
        view.addSubview(customNavigationBar)
        customNavigationBar.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }

        // 添加滚动视图
        view.addSubview(scrollView)
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 添加内容视图
        scrollView.addSubview(scrollContentView)
        scrollContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        // 设置用户信息区域
        setupUserInfoView()

        // 添加数据统计卡片 - 放在渐变背景内
        scrollContentView.addSubview(statsCardView)
        statsCardView.snp.makeConstraints { make in
            make.bottom.equalTo(backgroundGradientView.snp.bottom).offset(-16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(80)
        }

        // 添加功能按钮区域 - 在渐变背景下方
        scrollContentView.addSubview(functionsView)
        functionsView.snp.makeConstraints { make in
            make.top.equalTo(backgroundGradientView.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(80)
        }

        // 添加推荐商品区域
        scrollContentView.addSubview(recommendedProductsView)
        recommendedProductsView.snp.makeConstraints { make in
            make.top.equalTo(functionsView.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20)
        }
    }

    // 设置用户信息区域
    private func setupUserInfoView() {
        scrollContentView.addSubview(userInfoView)

        userInfoView.snp.makeConstraints { make in
            make.top.equalTo(customNavigationBar.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(100)
        }

        // 设置头像约束
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.equalToSuperview().offset(10)
            make.width.height.equalTo(60)
        }

        // 设置用户名约束
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(12)
            make.top.equalTo(avatarImageView.snp.top)
        }

        // 设置等级标签约束
        levelTagsContainer.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(8)
        }

        // 设置粉丝数约束
        fansLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.bottom.equalToSuperview().offset(-10)
        }

        // 设置作品数约束
        worksLabel.snp.makeConstraints { make in
            make.left.equalTo(fansLabel.snp.right).offset(20)
            make.bottom.equalTo(fansLabel)
        }

        // 设置位置按钮约束
        locationButton.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.top.equalTo(avatarImageView.snp.top)
            make.width.height.equalTo(24)
        }
    }



    // 加载用户数据
    private func loadUserData() {
        // 这里可以添加网络请求来获取用户数据
        // 暂时使用模拟数据
    }

    // MARK: - Helper Methods

    // 创建等级标签
    private func createLevelTag(text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.textColor = UIColor(hex: "#FF8D36")
        label.font = UIFont.systemFont(ofSize: 10)
        label.backgroundColor = .white
        label.layer.cornerRadius = 8
        label.clipsToBounds = true
        label.textAlignment = .center

        // 设置内边距
        label.snp.makeConstraints { make in
            make.height.equalTo(16)
            make.width.greaterThanOrEqualTo(50)
        }

        return label
    }

    // 创建功能按钮
    private func createFunctionButton(title: String, iconName: String) -> UIView {
        let containerView = UIView()

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: iconName)
        iconImageView.tintColor = UIColor(hex: "#666666")
        iconImageView.contentMode = .scaleAspectFit
        containerView.addSubview(iconImageView)

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.font = UIFont.systemFont(ofSize: 14)
        titleLabel.textAlignment = .center
        containerView.addSubview(titleLabel)

        iconImageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.height.equalTo(24)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
        }

        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(functionButtonTapped(_:)))
        containerView.addGestureRecognizer(tapGesture)
        containerView.isUserInteractionEnabled = true

        return containerView
    }

    // MARK: - Actions
    @objc private func backButtonTapped() {
        // 由于是在JXSegmentedListContainerView中，需要通过父控制器来处理返回
        if let parentVC = parent as? PromotionCenterMainViewController {
            parentVC.navigationController?.popViewController(animated: true)
        } else {
            navigationController?.popViewController(animated: true)
        }
    }

    @objc private func editButtonTapped() {
        print("点击了编辑按钮")
    }

    @objc private func rightButtonTapped() {
        print("点击了编辑按钮")
    }

    @objc private func functionButtonTapped(_ gesture: UITapGestureRecognizer) {
        guard let containerView = gesture.view else { return }
        // 根据按钮位置或标签来判断点击的是哪个功能
        print("功能按钮被点击")
    }

    // MARK: - Public Methods
    func addRightButtonToNavigationBar() {
        if let parentVC = parent as? PromotionCenterMainViewController {
            parentVC.addCustomRightButton(rightButton)
            rightButton.snp.makeConstraints { make in
                make.right.equalToSuperview().offset(-16)
                make.centerY.equalToSuperview()
                make.width.equalTo(60)
                make.height.equalTo(30)
            }
        }
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate
extension MyPromotionViewController: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }

    func listDidAppear() {
        print("我的带货页面显示")
        // 不再需要添加右侧按钮，因为使用自定义导航栏
    }

    func listDidDisappear() {
        print("我的带货页面隐藏")
        // 不再需要移除右侧按钮
    }
}

// MARK: - UICollectionViewDataSource, UICollectionViewDelegate
extension MyPromotionViewController: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return recommendedProducts.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "RecommendedProductCell", for: indexPath) as! RecommendedProductCell
        cell.configure(with: recommendedProducts[indexPath.item])
        return cell
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = (collectionView.bounds.width - 44) / 2 // 减去左右边距和中间间距
        let height = width * 1.4 + 60 // 图片比例 + 文字高度
        return CGSize(width: width, height: height)
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let product = recommendedProducts[indexPath.item]
        print("选择了商品：\(product["title"] ?? "")")
    }
}

// MARK: - RecommendedProductCell
class RecommendedProductCell: UICollectionViewCell {

    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 12 // 调整商品卡片圆角
        view.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowOpacity = 1
        view.layer.shadowRadius = 4
        return view
    }()

    private let productImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 8 // 商品图片圆角
        imageView.backgroundColor = UIColor(hex: "#F0F0F0")
        return imageView
    }()

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#333333")
        label.numberOfLines = 2
        return label
    }()

    private let subtitleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor(hex: "#999999")
        label.numberOfLines = 1
        return label
    }()

    private let priceLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.boldSystemFont(ofSize: 16)
        label.textColor = UIColor(hex: "#FF8D36")
        return label
    }()

    private let originalPriceLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor(hex: "#999999")
        return label
    }()

    private let actionButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("立即分享", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 12)
        button.backgroundColor = UIColor(hex: "#FF8D36")
        button.layer.cornerRadius = 12
        return button
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        contentView.addSubview(containerView)
        containerView.addSubview(productImageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(subtitleLabel)
        containerView.addSubview(priceLabel)
        containerView.addSubview(originalPriceLabel)
        containerView.addSubview(actionButton)

        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        productImageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(8)
            make.height.equalTo(productImageView.snp.width).multipliedBy(1.2)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(productImageView.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(8)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.left.right.equalToSuperview().inset(8)
        }

        priceLabel.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(4)
            make.left.equalToSuperview().offset(8)
        }

        originalPriceLabel.snp.makeConstraints { make in
            make.centerY.equalTo(priceLabel)
            make.left.equalTo(priceLabel.snp.right).offset(8)
        }

        actionButton.snp.makeConstraints { make in
            make.top.equalTo(priceLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(8)
            make.height.equalTo(24)
            make.bottom.equalToSuperview().offset(-8)
        }
    }

    func configure(with product: [String: String]) {
        titleLabel.text = product["title"]
        subtitleLabel.text = product["subtitle"]
        priceLabel.text = product["price"]

        if let originalPrice = product["originalPrice"] {
            let attributedString = NSMutableAttributedString(string: originalPrice)
            attributedString.addAttribute(.strikethroughStyle, value: NSUnderlineStyle.single.rawValue, range: NSRange(location: 0, length: originalPrice.count))
            originalPriceLabel.attributedText = attributedString
        }

        // 设置商品图片
        if let imageName = product["image"] {
            productImageView.image = UIImage(named: imageName) ?? UIImage(named: "product_placeholder")
        }
    }
}
